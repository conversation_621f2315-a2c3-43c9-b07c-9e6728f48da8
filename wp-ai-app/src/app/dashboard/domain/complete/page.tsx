"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from '../../../../components/providers/AuthProvider';
import DomainSiteMappingStep from '../../../../components/dashboard/domain/DomainSiteMappingStep';
import ProfileValidationModal from '../../../../components/modals/ProfileValidationModal';

export default function DomainCompletePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get("session_id");
  const domain = searchParams.get("domain");
  const siteId = searchParams.get("siteId");

  const { user, session, loading: authLoading, profile } = useAuth();

  const [status, setStatus] = useState("Processing your domain registration...");
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<'registration' | 'mapping' | 'complete'>('registration');
  const [registeredDomain, setRegisteredDomain] = useState<string | null>(null);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const hasRun = useRef(false);

  // Helper function to validate required profile fields
  const validateProfileFields = (profile: any) => {
    const requiredFields = [
      'first_name', 'last_name', 'address1', 'city',
      'state_province', 'postal_code', 'country', 'phone', 'email'
    ];

    const missingFields = requiredFields.filter(field => {
      const value = profile[field];
      return !value || value.toString().trim() === '';
    });

    return {
      isValid: missingFields.length === 0,
      missingFields
    };
  };

  useEffect(() => {
    const processDomainRegistration = async () => {
      if (hasRun.current || authLoading || !user || !profile) {
        console.log('Conditions not met for registration:', { hasRun: hasRun.current, authLoading, user: !!user, profile: !!profile });
        return;
      }
      hasRun.current = true;
      if (!sessionId || !domain) {
        setError("Missing required parameters (session ID or domain).");
        return;
      }

      console.log('Starting domain registration process:', { sessionId, domain, siteId, userId: user.id });

      // Validate profile fields before proceeding
      const validation = validateProfileFields(profile);
      if (!validation.isValid) {
        console.log('Missing required profile fields:', validation.missingFields);
        setStatus("Please complete your profile information to proceed with domain registration.");
        setShowProfileModal(true);
        return;
      }

      // Construct registrant info from profile
      const registrant: any = {
        FirstName: profile.first_name,
        LastName: profile.last_name,
        Address1: profile.address1,
        City: profile.city,
        StateProvince: profile.state_province,
        PostalCode: profile.postal_code,
        Country: profile.country,
        Phone: profile.phone || '',
        EmailAddress: profile.email,
      };
      // If .au domain, add AU fields (TODO: prompt user for these if needed)
      if (domain.endsWith('.au')) {
        registrant.AU_RegistrantIdNumber = '';
        registrant.AU_RegistrantIdType = '';
        // TODO: Prompt user for AU_RegistrantIdNumber and AU_RegistrantIdType
      }

      try {
        console.log('Proceeding with domain registration...');
        setStatus("Registering your domain...");
        // Register domain directly with Namecheap
        const registerRes = await fetch("/api/namecheap", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            domain,
            action: "register",
            siteId: siteId || 'pending',
            userId: user.id,
            stripeSessionId: sessionId,
            registrant,
          }),
        });

        console.log('Domain registration response status:', registerRes.status);

        const registerData = await registerRes.json();
        console.log('Domain registration data:', registerData);

        if (!registerRes.ok || registerData.error) {
          throw new Error(registerData.error || "Domain registration failed.");
        }

        // Domain registration successful - move to site mapping step
        setRegisteredDomain(domain);
        setCurrentStep('mapping');
        setStatus("Domain registered successfully! Now let's connect it to your site.");
      } catch (err: any) {
        console.error('Domain registration error:', err);
        setError(err.message || "An error occurred.");
        setStatus("");
      }
    };
    processDomainRegistration();
  }, [sessionId, domain, siteId, authLoading, user, router, profile]);

  // Handle profile modal completion
  const handleProfileComplete = () => {
    setShowProfileModal(false);
    setStatus("Processing your domain registration...");
    // Reset hasRun to allow the registration to proceed with updated profile
    hasRun.current = false;
  };

  // Handle site mapping completion
  const handleSiteMappingComplete = async (selectedSiteId: string) => {
    if (!registeredDomain || !user || !session) {
      throw new Error('Missing domain, user, or session information');
    }

    setStatus("Configuring DNS and mapping domain to site...");

    try {
      // Get the user's session token for authentication
      const token = session.access_token;

      if (!token) {
        throw new Error('Authentication token not available');
      }

      const response = await fetch('/api/domain-mapping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          domainName: registeredDomain,
          siteId: selectedSiteId
        }),
      });

      const data = await response.json();

      if (!response.ok || data.error) {
        throw new Error(data.error || 'Failed to map domain to site');
      }

      setCurrentStep('complete');
      setStatus("Domain setup complete! Redirecting to dashboard...");
      setTimeout(() => router.replace("/dashboard"), 2000);

    } catch (error: any) {
      console.error('Site mapping error:', error);
      throw error; // Re-throw to be handled by the DomainSiteMappingStep component
    }
  };

  if (authLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50">
        <div className="w-full max-w-md p-6 text-center bg-white rounded-lg shadow-md sm:p-8">
          <h1 className="mb-4 text-xl font-bold text-gray-800 sm:text-2xl">Verifying Authentication</h1>
          <p>Please wait while we confirm your session...</p>
        </div>
      </div>
    );
  }

  // Show site mapping step if domain registration is complete
  if (currentStep === 'mapping' && registeredDomain) {
    return (
      <div className="min-h-screen p-4 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <DomainSiteMappingStep
            domainName={registeredDomain}
            onComplete={handleSiteMappingComplete}
          />
        </div>
      </div>
    );
  }

  // Show registration progress or completion
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50">
      <div className="w-full max-w-md p-6 text-center bg-white rounded-lg shadow-md sm:p-8">
        <h1 className="mb-4 text-xl font-bold text-gray-800 sm:text-2xl">
          {currentStep === 'complete' ? 'Domain Setup Complete!' : 'Completing Domain Registration'}
        </h1>
        {status && <p className="mb-4 text-sm text-gray-700 sm:text-base">{status}</p>}
        {error && (
          <div className="mb-4 text-sm font-semibold text-red-600 break-words sm:text-base">{error}</div>
        )}
        {!error && currentStep !== 'complete' && (
          <div className="flex items-center justify-center mb-4">
            <svg className="w-6 h-6 text-green-600 animate-spin sm:h-8 sm:w-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
            </svg>
          </div>
        )}
        {currentStep === 'complete' && (
          <div className="flex items-center justify-center mb-4">
            <svg className="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
        )}
        {error && (
          <button
            className="w-full px-4 py-2 mt-4 text-sm text-white transition-colors bg-blue-600 rounded sm:w-auto sm:text-base hover:bg-blue-700"
            onClick={() => router.replace("/dashboard/domain")}
          >
            Back to Domain Page
          </button>
        )}
      </div>

      {/* Profile Validation Modal */}
      <ProfileValidationModal
        isOpen={showProfileModal}
        onComplete={handleProfileComplete}
        onCancel={() => {
          setShowProfileModal(false);
          setError("Domain registration cancelled. Profile information is required.");
          router.replace("/dashboard/domain");
        }}
      />
    </div>
  );
}