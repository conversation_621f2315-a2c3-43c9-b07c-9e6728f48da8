'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../../components/providers/AuthProvider';
import { createClient } from '@supabase/supabase-js';
import { AlertCircle, CreditCard, FileText, Calendar, CheckCircle, XCircle, Loader2 } from 'lucide-react';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// TypeScript interfaces for billing data
interface BillingSubscription {
  status: string;
  plan: string;
  amount: number;
  currency: string;
  nextBillingDate: string | null;
}

interface BillingInvoice {
  id: string;
  date: string;
  amount: number;
  status: string;
  url: string | null;
}

interface BillingPaymentMethod {
  id: string;
  type: string;
  last4: string | null;
  brand: string | null;
  isDefault: boolean;
}

interface BillingData {
  subscription: BillingSubscription | null;
  invoices: BillingInvoice[];
  paymentMethods: BillingPaymentMethod[];
}

interface ApiError {
  message: string;
  status?: number;
}

export default function BillingPage() {
  const { profile, loading: authLoading } = useAuth();
  const [activeTab, setActiveTab] = useState<'subscription' | 'invoices' | 'paymentMethods'>('subscription');
  const [billingData, setBillingData] = useState<BillingData | null>(null);
  const [billingLoading, setBillingLoading] = useState(true);
  const [error, setError] = useState<ApiError | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    if (!authLoading && !profile) {
      window.location.href = '/login';
    }
  }, [profile, authLoading]);

  const fetchBillingData = async () => {
    try {
      setBillingLoading(true);
      setError(null);

      // Get the current session token
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/stripe/billing-data', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Failed to fetch billing data' }));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data: BillingData = await response.json();
      setBillingData(data);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch billing data';
      setError({ message: errorMessage, status: err.status });
      console.error('Billing data fetch error:', err);
    } finally {
      setBillingLoading(false);
    }
  };

  useEffect(() => {
    if (!authLoading && profile) {
      fetchBillingData();
    }
  }, [authLoading, profile, retryCount]);

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
  };

  // Loading state component
  const LoadingState = () => (
    <div className="flex flex-col items-center justify-center p-8 space-y-4">
      <Loader2 className="w-8 h-8 text-indigo-600 animate-spin" />
      <p className="text-gray-600">Loading billing information...</p>
    </div>
  );

  // Error state component
  const ErrorState = ({ error, onRetry }: { error: ApiError; onRetry: () => void }) => (
    <div className="flex flex-col items-center justify-center p-8 space-y-4">
      <AlertCircle className="w-12 h-12 text-red-500" />
      <div className="text-center">
        <h3 className="mb-2 text-lg font-semibold text-gray-900">Unable to load billing data</h3>
        <p className="mb-4 text-gray-600">{error.message}</p>
        <button
          onClick={onRetry}
          className="px-4 py-2 text-white transition-colors bg-indigo-600 rounded-md hover:bg-indigo-700"
        >
          Try Again
        </button>
      </div>
    </div>
  );

  // Empty state component
  const EmptyState = ({ title, description, icon: Icon }: { title: string; description: string; icon: any }) => (
    <div className="flex flex-col items-center justify-center p-8 space-y-4">
      <Icon className="w-12 h-12 text-gray-400" />
      <div className="text-center">
        <h3 className="mb-2 text-lg font-semibold text-gray-900">{title}</h3>
        <p className="text-gray-600">{description}</p>
      </div>
    </div>
  );

  // Subscription content
  const renderSubscriptionContent = () => {
    if (!billingData?.subscription) {
      return (
        <EmptyState
          title="No Active Subscription"
          description="You don't have an active subscription. Subscribe to a plan to get started."
          icon={Calendar}
        />
      );
    }

    const { subscription } = billingData;
    const statusColor = subscription.status === 'active' ? 'text-green-600' : 'text-yellow-600';
    const StatusIcon = subscription.status === 'active' ? CheckCircle : XCircle;

    return (
      <div className="space-y-4 sm:space-y-6">
        <h2 className="mb-4 text-lg font-semibold sm:text-xl">Current Subscription</h2>
        <div className="p-4 space-y-3 rounded-lg bg-gray-50 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <span className="text-sm text-gray-600 sm:text-base">Status:</span>
            <div className="flex items-center space-x-2">
              <StatusIcon className={`w-4 h-4 ${statusColor}`} />
              <span className={`text-sm font-medium capitalize sm:text-base ${statusColor}`}>
                {subscription.status}
              </span>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <span className="text-sm text-gray-600 sm:text-base">Plan:</span>
            <span className="text-sm font-medium sm:text-base">{subscription.plan} Plan</span>
          </div>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <span className="text-sm text-gray-600 sm:text-base">Amount:</span>
            <span className="text-sm font-medium sm:text-base">
              ${subscription.amount.toFixed(2)} {subscription.currency.toUpperCase()}
            </span>
          </div>
          {subscription.nextBillingDate && (
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <span className="text-sm text-gray-600 sm:text-base">Next Billing Date:</span>
              <span className="text-sm font-medium sm:text-base">
                {new Date(subscription.nextBillingDate).toLocaleDateString()}
              </span>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Invoices content
  const renderInvoicesContent = () => {
    if (!billingData?.invoices || billingData.invoices.length === 0) {
      return (
        <EmptyState
          title="No Billing History"
          description="You don't have any invoices yet. Your billing history will appear here once you have active subscriptions."
          icon={FileText}
        />
      );
    }

    return (
      <div className="space-y-4 sm:space-y-6">
        <h2 className="mb-4 text-lg font-semibold sm:text-xl">Billing History</h2>
        <div className="space-y-3">
          {billingData.invoices.map((invoice) => (
            <div key={invoice.id} className="p-4 border border-gray-200 rounded-lg bg-gray-50">
              <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                <div className="flex flex-col space-y-1 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
                  <span className="text-sm font-medium sm:text-base">
                    {new Date(invoice.date).toLocaleDateString()}
                  </span>
                  <span className="text-sm text-gray-600 sm:text-base">
                    ${invoice.amount.toFixed(2)}
                  </span>
                  <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
                    invoice.status === 'paid' 
                      ? 'text-green-800 bg-green-100' 
                      : 'text-yellow-800 bg-yellow-100'
                  }`}>
                    {invoice.status}
                  </span>
                </div>
                {invoice.url && (
                  <a
                    href={invoice.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm font-medium text-blue-500 hover:underline sm:text-base"
                  >
                    View Invoice
                  </a>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Payment methods content
  const renderPaymentMethodsContent = () => {
    if (!billingData?.paymentMethods || billingData.paymentMethods.length === 0) {
      return (
        <EmptyState
          title="No Payment Methods"
          description="You haven't added any payment methods yet. Add a payment method to manage your billing."
          icon={CreditCard}
        />
      );
    }

    return (
      <div className="space-y-4 sm:space-y-6">
        <h2 className="mb-4 text-lg font-semibold sm:text-xl">Payment Methods</h2>
        <div className="space-y-3">
          {billingData.paymentMethods.map((pm) => (
            <div key={pm.id} className="p-4 border border-gray-200 rounded-lg bg-gray-50">
              <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                <div className="flex flex-col space-y-1 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2">
                  <span className="text-sm font-medium sm:text-base">
                    {pm.brand} ending in {pm.last4}
                  </span>
                  {pm.isDefault && (
                    <span className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full">
                      Default
                    </span>
                  )}
                </div>
                <button className="self-start text-sm font-medium text-blue-500 hover:text-blue-700 sm:self-auto">
                  Manage
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderContent = () => {
    if (billingLoading) {
      return <LoadingState />;
    }

    if (error) {
      return <ErrorState error={error} onRetry={handleRetry} />;
    }

    if (!billingData) {
      return (
        <EmptyState
          title="No Billing Data"
          description="Unable to load billing information. Please try again later."
          icon={AlertCircle}
        />
      );
    }

    switch (activeTab) {
      case 'subscription':
        return renderSubscriptionContent();
      case 'invoices':
        return renderInvoicesContent();
      case 'paymentMethods':
        return renderPaymentMethodsContent();
      default:
        return null;
    }
  };

  if (authLoading || !profile) {
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <Loader2 className="w-8 h-8 text-indigo-600 animate-spin" />
        <p className="text-gray-600">Checking authentication...</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 sm:p-6">
        <h1 className="mb-2 text-xl font-bold sm:text-2xl">Billing</h1>
        <div className="text-sm text-gray-700 sm:text-base">
          Logged in as: <span className="font-semibold break-all">{profile.email}</span>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b">
        <nav className="flex overflow-x-auto">
          <button
            onClick={() => setActiveTab('subscription')}
            className={`whitespace-nowrap pb-4 px-4 sm:px-6 pt-4 border-b-2 font-medium text-sm sm:text-base flex-shrink-0 ${
              activeTab === 'subscription'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Subscription
          </button>
          <button
            onClick={() => setActiveTab('invoices')}
            className={`whitespace-nowrap pb-4 px-4 sm:px-6 pt-4 border-b-2 font-medium text-sm sm:text-base flex-shrink-0 ${
              activeTab === 'invoices'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Invoices
          </button>
          <button
            onClick={() => setActiveTab('paymentMethods')}
            className={`whitespace-nowrap pb-4 px-4 sm:px-6 pt-4 border-b-2 font-medium text-sm sm:text-base flex-shrink-0 ${
              activeTab === 'paymentMethods'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <span className="hidden sm:inline">Payment Methods</span>
            <span className="sm:hidden">Payment</span>
          </button>
        </nav>
      </div>

      {/* Content */}
      <div className="p-4 sm:p-6">
        {renderContent()}
      </div>
    </div>
  );
}
