"use client";

import React, { useState, useEffect } from 'react';
import { Globe, Calendar, ExternalLink, Settings, AlertCircle, CheckCircle, Clock, XCircle } from 'lucide-react';
import { useAuth } from '../../providers/AuthProvider';

interface Domain {
  id: string;
  domain_name: string;
  user_id: string;
  site_id: string | null;
  status: 'pending' | 'registered' | 'active' | 'expired' | 'failed';
  registration_date: string | null;
  expiry_date: string | null;
  auto_renew: boolean;
  price_paid: number | null;
  currency: string;
  dns_configured: boolean;
  cname_target: string | null;
  created_at: string;
  updated_at: string;
  site_name?: string | null;
}

interface MyDomainsSectionProps {
  onRefresh?: () => void;
}

const MyDomainsSection: React.FC<MyDomainsSectionProps> = ({ onRefresh }) => {
  const { user, session } = useAuth();
  const [domains, setDomains] = useState<Domain[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDomains();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const fetchDomains = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/domains', {
        headers: {
          'Authorization': `Bearer ${session?.access_token || ''}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch domains');
      }

      const data = await response.json();
      setDomains(data.domains || []);
    } catch (err: any) {
      console.error('Error fetching domains:', err);
      setError(err.message || 'Failed to load domains');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: Domain['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'registered':
        return <CheckCircle className="w-5 h-5 text-blue-500" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'expired':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: Domain['status']) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'registered':
        return 'Registered';
      case 'pending':
        return 'Pending';
      case 'expired':
        return 'Expired';
      case 'failed':
        return 'Failed';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status: Domain['status']) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-50';
      case 'registered':
        return 'text-blue-600 bg-blue-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'expired':
        return 'text-red-600 bg-red-50';
      case 'failed':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const formatPrice = (price: number | null, currency: string) => {
    if (!price) return 'N/A';
    return `${currency} $${price.toFixed(2)}`;
  };

  const isExpiringSoon = (expiryDate: string | null) => {
    if (!expiryDate) return false;
    const expiry = new Date(expiryDate);
    const now = new Date();
    const daysUntilExpiry = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Clock className="w-8 h-8 mr-3 text-gray-400 animate-spin" />
        <span className="text-gray-600">Loading your domains...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 border border-red-200 rounded-lg bg-red-50">
        <div className="flex items-center mb-2">
          <AlertCircle className="w-5 h-5 mr-2 text-red-500" />
          <span className="font-medium text-red-700">Error Loading Domains</span>
        </div>
        <p className="mb-4 text-sm text-red-600">{error}</p>
        <button
          onClick={fetchDomains}
          className="px-4 py-2 text-white transition-colors bg-red-600 rounded-md hover:bg-red-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (domains.length === 0) {
    return (
      <div className="py-12 text-center">
        <div className="max-w-md p-8 mx-auto rounded-lg bg-gray-50">
          <Globe className="w-16 h-16 mx-auto mb-4 text-gray-400" />
          <h3 className="mb-2 text-xl font-semibold text-gray-600">No Domains Yet</h3>
          <p className="mb-6 text-gray-500">
            You haven&apos;t registered any domains yet. Start by registering your first domain below.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">My Domains</h2>
          <p className="text-gray-600">Manage your registered domains and their settings</p>
        </div>
        <button
          onClick={fetchDomains}
          className="px-4 py-2 text-gray-700 transition-colors border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Refresh
        </button>
      </div>

      {/* Domains Grid */}
      <div className="grid gap-6">
        {domains.map((domain) => (
          <div key={domain.id} className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
              {/* Domain Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center mb-2 space-x-3">
                  <h3 className="text-xl font-semibold text-gray-800 truncate">
                    {domain.domain_name}
                  </h3>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(domain.status)}
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(domain.status)}`}>
                      {getStatusText(domain.status)}
                    </span>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 gap-3 text-sm sm:grid-cols-2 lg:grid-cols-4">
                  <div>
                    <span className="text-gray-500">Registered:</span>
                    <p className="font-medium">{formatDate(domain.registration_date)}</p>
                  </div>
                  
                  <div>
                    <span className="text-gray-500">Expires:</span>
                    <p className={`font-medium ${isExpiringSoon(domain.expiry_date) ? 'text-amber-600' : ''}`}>
                      {formatDate(domain.expiry_date)}
                      {isExpiringSoon(domain.expiry_date) && (
                        <span className="ml-1 text-amber-600">⚠️</span>
                      )}
                    </p>
                  </div>
                  
                  <div>
                    <span className="text-gray-500">Mapped to:</span>
                    <p className="font-medium">
                      {domain.site_name ? (
                        <span className="text-green-600">{domain.site_name}</span>
                      ) : (
                        <span className="text-gray-400">Not mapped</span>
                      )}
                    </p>
                  </div>
                  
                  <div>
                    <span className="text-gray-500">Price paid:</span>
                    <p className="font-medium">{formatPrice(domain.price_paid, domain.currency)}</p>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex flex-col gap-2 sm:flex-row lg:ml-6">
                {domain.site_name && (
                  <a
                    href={`https://${domain.domain_name}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-3 py-2 text-sm text-gray-700 transition-colors border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    <ExternalLink className="w-4 h-4 mr-1" />
                    Visit
                  </a>
                )}
                
                <button className="inline-flex items-center px-3 py-2 text-sm text-gray-700 transition-colors border border-gray-300 rounded-md hover:bg-gray-50">
                  <Settings className="w-4 h-4 mr-1" />
                  Manage
                </button>
              </div>
            </div>

            {/* Additional Info */}
            <div className="pt-4 mt-4 border-t border-gray-100">
              <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500">
                <span>DNS: {domain.dns_configured ? '✅ Configured' : '⏳ Pending'}</span>
                {/* <span>Auto-renew: {domain.auto_renew ? '✅ Enabled' : '❌ Disabled'}</span> */}
                {domain.cname_target && (
                  <span>CNAME: {domain.cname_target}</span>
                )}
              </div>
            </div>

            {/* Expiry Warning */}
            {isExpiringSoon(domain.expiry_date) && (
              <div className="p-3 mt-4 border rounded-md bg-amber-50 border-amber-200">
                <div className="flex items-center">
                  <AlertCircle className="w-4 h-4 mr-2 text-amber-600" />
                  <span className="text-sm font-medium text-amber-800">
                    Domain expires soon! Consider renewing to avoid service interruption.
                  </span>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default MyDomainsSection;
