import { useState, useEffect, useCallback, useRef } from 'react';

interface DomainCheckResult {
  Domain: string;
  Available: boolean;
  IsPremiumName: boolean;
  Price?: number;
  Error?: string;
}

interface UseDomainSearchOptions {
  debounceMs?: number;
  minLength?: number;
}

interface UseDomainSearchReturn {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  results: DomainCheckResult[];
  loading: boolean;
  error: string | null;
  clearResults: () => void;
  hasSearched: boolean;
}

export const useDomainSearch = (
  options: UseDomainSearchOptions = {}
): UseDomainSearchReturn => {
  const { debounceMs = 800, minLength = 2 } = options;
  
  const [searchTerm, setSearchTerm] = useState('');
  const [results, setResults] = useState<DomainCheckResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasSearched, setHasSearched] = useState(false);
  
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const performSearch = useCallback(async (term: string) => {
    // Cancel any previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this request
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);
    setResults([]);

    try {
      const response = await fetch('/api/namecheap', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain: term.trim(), action: 'check' }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        const { error } = await response.json();
        throw new Error(error || 'Failed to check domain availability.');
      }

      const data = await response.json();
      
      // Only update results if this request wasn't aborted
      if (!abortControllerRef.current.signal.aborted) {
        setResults(data.results || []);
        setHasSearched(true);
      }
    } catch (err: any) {
      // Don't show error if request was aborted (user typed more)
      if (err.name !== 'AbortError' && !abortControllerRef.current?.signal.aborted) {
        console.error('Domain search error:', err);
        setError(err.message || 'Failed to search domains');
        setResults([]);
        setHasSearched(true);
      }
    } finally {
      if (!abortControllerRef.current?.signal.aborted) {
        setLoading(false);
      }
    }
  }, []);

  // Debounced search effect
  useEffect(() => {
    // Clear previous timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Don't search if term is too short
    if (searchTerm.trim().length < minLength) {
      setResults([]);
      setError(null);
      setHasSearched(false);
      setLoading(false);
      return;
    }

    // Set new timer
    debounceTimerRef.current = setTimeout(() => {
      performSearch(searchTerm);
    }, debounceMs);

    // Cleanup function
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [searchTerm, debounceMs, minLength, performSearch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const clearResults = useCallback(() => {
    setResults([]);
    setError(null);
    setHasSearched(false);
    setLoading(false);
    
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  return {
    searchTerm,
    setSearchTerm,
    results,
    loading,
    error,
    clearResults,
    hasSearched,
  };
};
